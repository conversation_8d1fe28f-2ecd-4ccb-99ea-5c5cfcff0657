[2025-06-17 13:37:37.462] [info] Updating Model file from 40000 to latest format...
[2025-06-17 13:37:37.505] [info] Loaded model Pose2Sim_WithContactsAndMuscles from file /home/<USER>/miniconda3/envs/Pose2Sim/lib/python3.10/site-packages/Pose2Sim/OpenSim_Setup/Model_Pose2Sim_contacts_muscles.osim
[2025-06-17 13:37:37.719] [info] Processing subject Pose2Sim_scaled...
[2025-06-17 13:37:37.719] [info] Step 1: Loading generic model
[2025-06-17 13:37:37.785] [info] Loaded model Pose2Sim_WithContactsAndMuscles from file /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM.osim
[2025-06-17 13:37:37.898] [info] Step 2: Scaling generic model
[2025-06-17 13:37:38.111] [info] Wrote model file '/home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM.osim' from model.
[2025-06-17 13:37:38.219] [info] Loaded model Pose2Sim_scaled from file /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM.osim
[2025-06-17 13:37:38.308] [cout] [info] 
               MODEL: Pose2Sim_scaled
         coordinates: 62
              forces: 330
           actuators: 318
             muscles: 318
            analyses: 0
              probes: 0
              bodies: 30
              joints: 30
         constraints: 20
             markers: 52
         controllers: 0
  contact geometries: 13
misc modelcomponents: 0
[2025-06-17 13:37:38.308] [info] Running tool .
[2025-06-17 13:37:41.583] [info] Frame 0 (t = 0.566667):	 total squared error = 0.0246436, marker error: RMS = 0.0239396, max = 0.0728305 (r_shoulder_study)
[2025-06-17 13:37:42.259] [info] Frame 1 (t = 0.583333):	 total squared error = 0.0203408, marker error: RMS = 0.0217495, max = 0.068476 (r_shoulder_study)
[2025-06-17 13:37:42.659] [info] Frame 2 (t = 0.6):	 total squared error = 0.0160018, marker error: RMS = 0.0192908, max = 0.0558893 (r_shoulder_study)
[2025-06-17 13:37:43.251] [info] Frame 3 (t = 0.616667):	 total squared error = 0.0143995, marker error: RMS = 0.0182995, max = 0.0496778 (r_shoulder_study)
[2025-06-17 13:37:43.565] [info] Frame 4 (t = 0.633333):	 total squared error = 0.0141503, marker error: RMS = 0.0181405, max = 0.0444246 (r_shoulder_study)
[2025-06-17 13:37:44.065] [info] Frame 5 (t = 0.65):	 total squared error = 0.0139085, marker error: RMS = 0.0179848, max = 0.0414531 (r_shoulder_study)
[2025-06-17 13:37:44.535] [info] Frame 6 (t = 0.666667):	 total squared error = 0.0140879, marker error: RMS = 0.0181005, max = 0.0396963 (r_shoulder_study)
[2025-06-17 13:37:45.027] [info] Frame 7 (t = 0.683333):	 total squared error = 0.0141671, marker error: RMS = 0.0181512, max = 0.038815 (r_shoulder_study)
[2025-06-17 13:37:45.531] [info] Frame 8 (t = 0.7):	 total squared error = 0.0144466, marker error: RMS = 0.0183294, max = 0.0395016 (r_shoulder_study)
[2025-06-17 13:37:45.983] [info] Frame 9 (t = 0.716667):	 total squared error = 0.0147281, marker error: RMS = 0.0185072, max = 0.0392666 (r_shoulder_study)
[2025-06-17 13:37:46.475] [info] Frame 10 (t = 0.733333):	 total squared error = 0.0151887, marker error: RMS = 0.0187943, max = 0.0400179 (r_shoulder_study)
[2025-06-17 13:37:46.631] [info] Frame 11 (t = 0.75):	 total squared error = 0.015644, marker error: RMS = 0.0190739, max = 0.041425 (r_shoulder_study)
[2025-06-17 13:37:47.291] [info] Frame 12 (t = 0.766667):	 total squared error = 0.0160286, marker error: RMS = 0.0193070, max = 0.0419314 (r_shoulder_study)
[2025-06-17 13:37:47.775] [info] Frame 13 (t = 0.783333):	 total squared error = 0.0162748, marker error: RMS = 0.0194547, max = 0.04278 (r_shoulder_study)
[2025-06-17 13:37:48.528] [info] Frame 14 (t = 0.8):	 total squared error = 0.0163387, marker error: RMS = 0.0194928, max = 0.0438070 (r_shoulder_study)
[2025-06-17 13:37:49.207] [info] Frame 15 (t = 0.816667):	 total squared error = 0.0163049, marker error: RMS = 0.0194726, max = 0.0441289 (r_shoulder_study)
[2025-06-17 13:37:49.993] [info] Frame 16 (t = 0.833333):	 total squared error = 0.0162095, marker error: RMS = 0.0194156, max = 0.0442715 (r_shoulder_study)
[2025-06-17 13:37:50.375] [info] Frame 17 (t = 0.85):	 total squared error = 0.0160208, marker error: RMS = 0.0193022, max = 0.0437897 (r_shoulder_study)
[2025-06-17 13:37:51.283] [info] Frame 18 (t = 0.866667):	 total squared error = 0.0157731, marker error: RMS = 0.0191525, max = 0.043529 (r_shoulder_study)
[2025-06-17 13:37:51.823] [info] Frame 19 (t = 0.883333):	 total squared error = 0.01543, marker error: RMS = 0.018943, max = 0.0427114 (r_shoulder_study)
[2025-06-17 13:37:52.447] [info] Frame 20 (t = 0.9):	 total squared error = 0.0150277, marker error: RMS = 0.0186944, max = 0.0418383 (r_shoulder_study)
[2025-06-17 13:37:52.763] [info] Frame 21 (t = 0.916667):	 total squared error = 0.0146382, marker error: RMS = 0.0184506, max = 0.0405755 (r_shoulder_study)
[2025-06-17 13:37:53.464] [info] Frame 22 (t = 0.933333):	 total squared error = 0.0141956, marker error: RMS = 0.0181695, max = 0.0408972 (r_shoulder_study)
[2025-06-17 13:37:54.780] [info] Frame 23 (t = 0.95):	 total squared error = 0.0137306, marker error: RMS = 0.0178694, max = 0.0417437 (r_shoulder_study)
[2025-06-17 13:37:55.135] [info] Frame 24 (t = 0.966667):	 total squared error = 0.0133276, marker error: RMS = 0.0176052, max = 0.0415455 (r_shoulder_study)
[2025-06-17 13:37:55.987] [info] Frame 25 (t = 0.983333):	 total squared error = 0.0130376, marker error: RMS = 0.0174127, max = 0.0426661 (r_shoulder_study)
[2025-06-17 13:37:55.993] [info] InverseKinematicsTool completed 26 frames in 15 second(s).
[2025-06-17 13:37:56.103] [info] Updating Model file from 40000 to latest format...
[2025-06-17 13:37:56.155] [info] Loaded model Pose2Sim_WithContactsAndMuscles from file /home/<USER>/miniconda3/envs/Pose2Sim/lib/python3.10/site-packages/Pose2Sim/OpenSim_Setup/Model_Pose2Sim_contacts_muscles.osim
[2025-06-17 13:37:56.363] [info] Processing subject Pose2Sim_scaled...
[2025-06-17 13:37:56.363] [info] Step 1: Loading generic model
[2025-06-17 13:37:56.431] [info] Loaded model Pose2Sim_WithContactsAndMuscles from file /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM.osim
[2025-06-17 13:37:56.558] [info] Step 2: Scaling generic model
[2025-06-17 13:37:56.778] [info] Wrote model file '/home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM.osim' from model.
[2025-06-17 13:37:56.907] [info] Loaded model Pose2Sim_scaled from file /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM.osim
[2025-06-17 13:37:57.010] [cout] [info] 
               MODEL: Pose2Sim_scaled
         coordinates: 62
              forces: 330
           actuators: 318
             muscles: 318
            analyses: 0
              probes: 0
              bodies: 30
              joints: 30
         constraints: 20
             markers: 52
         controllers: 0
  contact geometries: 13
misc modelcomponents: 0
[2025-06-17 13:37:57.010] [info] Running tool .
[2025-06-17 13:37:59.075] [info] Frame 0 (t = 0.566667):	 total squared error = 0.0261986, marker error: RMS = 0.0246834, max = 0.0492757 (L_toe_study)
[2025-06-17 13:37:59.583] [info] Frame 1 (t = 0.583333):	 total squared error = 0.0210192, marker error: RMS = 0.0221093, max = 0.044921 (L_lelbow_study)
[2025-06-17 13:38:00.127] [info] Frame 2 (t = 0.6):	 total squared error = 0.0153683, marker error: RMS = 0.0189051, max = 0.0390462 (L_lelbow_study)
[2025-06-17 13:38:00.503] [info] Frame 3 (t = 0.616667):	 total squared error = 0.0127128, marker error: RMS = 0.0171944, max = 0.0372185 (L_shoulder_study)
[2025-06-17 13:38:00.947] [info] Frame 4 (t = 0.633333):	 total squared error = 0.0115541, marker error: RMS = 0.0163921, max = 0.0383524 (r_shoulder_study)
[2025-06-17 13:38:02.083] [info] Frame 5 (t = 0.65):	 total squared error = 0.0119316, marker error: RMS = 0.0166577, max = 0.0404377 (r_shoulder_study)
[2025-06-17 13:38:03.131] [info] Frame 6 (t = 0.666667):	 total squared error = 0.013357, marker error: RMS = 0.0176247, max = 0.0452069 (r_shoulder_study)
[2025-06-17 13:38:03.660] [info] Frame 7 (t = 0.683333):	 total squared error = 0.0146724, marker error: RMS = 0.0184721, max = 0.047959 (r_shoulder_study)
[2025-06-17 13:38:05.087] [info] Frame 8 (t = 0.7):	 total squared error = 0.0150365, marker error: RMS = 0.0186999, max = 0.0433254 (r_shoulder_study)
[2025-06-17 13:38:06.672] [info] Frame 9 (t = 0.716667):	 total squared error = 0.0141093, marker error: RMS = 0.0181142, max = 0.0323961 (r_shoulder_study)
[2025-06-17 13:38:08.544] [info] Frame 10 (t = 0.733333):	 total squared error = 0.0128714, marker error: RMS = 0.0173013, max = 0.0290993 (L_shoulder_study)
[2025-06-17 13:38:09.280] [info] Frame 11 (t = 0.75):	 total squared error = 0.0125733, marker error: RMS = 0.0170998, max = 0.0302137 (L_shoulder_study)
[2025-06-17 13:38:10.097] [info] Frame 12 (t = 0.766667):	 total squared error = 0.0126741, marker error: RMS = 0.0171682, max = 0.0319919 (L_shoulder_study)
[2025-06-17 13:38:10.851] [info] Frame 13 (t = 0.783333):	 total squared error = 0.0126984, marker error: RMS = 0.0171846, max = 0.0334781 (L_shoulder_study)
[2025-06-17 13:38:11.263] [info] Frame 14 (t = 0.8):	 total squared error = 0.014455, marker error: RMS = 0.0183347, max = 0.0426167 (L_shoulder_study)
[2025-06-17 13:38:11.856] [info] Frame 15 (t = 0.816667):	 total squared error = 0.021423, marker error: RMS = 0.0223206, max = 0.0645624 (L_shoulder_study)
[2025-06-17 13:38:18.696] [info] Frame 16 (t = 0.833333):	 total squared error = 0.0230226, marker error: RMS = 0.0231389, max = 0.0723702 (r_shoulder_study)
[2025-06-17 13:38:19.455] [info] Frame 17 (t = 0.85):	 total squared error = 0.0150989, marker error: RMS = 0.0187387, max = 0.0404923 (r_shoulder_study)
[2025-06-17 13:38:20.159] [info] Frame 18 (t = 0.866667):	 total squared error = 0.0126047, marker error: RMS = 0.0171211, max = 0.029594 (L_thigh1_study)
[2025-06-17 13:38:20.772] [info] Frame 19 (t = 0.883333):	 total squared error = 0.0132756, marker error: RMS = 0.0175708, max = 0.0317319 (L_thigh1_study)
[2025-06-17 13:38:21.139] [info] Frame 20 (t = 0.9):	 total squared error = 0.0141127, marker error: RMS = 0.0181164, max = 0.0334757 (L_thigh1_study)
[2025-06-17 13:38:21.311] [info] Frame 21 (t = 0.916667):	 total squared error = 0.0151676, marker error: RMS = 0.0187812, max = 0.0358079 (r_thigh1_study)
[2025-06-17 13:38:21.964] [info] Frame 22 (t = 0.933333):	 total squared error = 0.015885, marker error: RMS = 0.0192203, max = 0.037886 (r_thigh1_study)
[2025-06-17 13:38:22.496] [info] Frame 23 (t = 0.95):	 total squared error = 0.0167049, marker error: RMS = 0.01971, max = 0.0396464 (r_thigh1_study)
[2025-06-17 13:38:23.128] [info] Frame 24 (t = 0.966667):	 total squared error = 0.0175523, marker error: RMS = 0.0202038, max = 0.0409933 (r_thigh1_study)
[2025-06-17 13:38:23.455] [info] Frame 25 (t = 0.983333):	 total squared error = 0.0185771, marker error: RMS = 0.0207852, max = 0.0419068 (r_thigh1_study)
[2025-06-17 13:38:23.460] [info] InverseKinematicsTool completed 26 frames in 24 second(s).
