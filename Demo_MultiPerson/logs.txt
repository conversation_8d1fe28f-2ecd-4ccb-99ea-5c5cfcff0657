

=====================================================================
RUNNING ALL.
On Tuesday 17. June 2025, 13:35:02
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson

=====================================================================



=====================================================================
Running calibration...
=====================================================================

---------------------------------------------------------------------
Camera calibration
On Tuesday 17. June 2025, 13:35:03
Calibration directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/calibration
---------------------------------------------------------------------

Converting /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/calibration/Calib.qca.txt to .toml calibration file...

--> Residual (RMS) calibration errors for each camera are respectively [0.221, 0.235, 0.171, 0.191] px, 
which corresponds to [0.402, 0.445, 0.45, 0.505] mm.

Calibration file is stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/calibration/Calib_qualisys.toml.

Calibration took 0.01 seconds.



=====================================================================
Running pose estimation...
=====================================================================

---------------------------------------------------------------------
Pose estimation for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:35:03
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------

Inference run only every 4 frames. Inbetween, pose estimation tracks previously detected points.

Estimating pose...
Using HALPE_26 model (body and feet) for pose estimation.

No valid CUDA installation found: using OpenVINO backend with CPU.

Pose tracking set up for "Body_with_feet" model.
Mode: balanced.
Tracking is performed with sports2d.

Found video files with mp4 extension.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose/cam01_pose.mp4.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose/cam02_pose.mp4.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose/cam03_pose.mp4.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose/cam04_pose.mp4.

Pose estimation took 00h00m36s.



=====================================================================
Running synchronization...
=====================================================================

---------------------------------------------------------------------
Camera synchronization for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:35:40
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------

Synchronization is calculated on the whole sequence. This may take a while.
Keypoints used to compute the best synchronization offset: ['RWrist'].
These keypoints are filtered with a Butterworth filter (cut-off frequency: 6 Hz, order: 4).
They are removed when their likelihood is below 0.4.

Synchronizing...

--> Camera cam01 and cam02: -39 frames offset, correlation 0.64.
--> Camera cam01 and cam03: 39 frames offset, correlation 0.81.
--> Camera cam01 and cam04: -34 frames offset, correlation 0.41.
Saving synchronized json files to the pose-sync folder.
Synchronized json files saved in /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-sync.

Synchronization took 00h00m00s.



=====================================================================
Running person association...
=====================================================================

---------------------------------------------------------------------
Associating persons for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:35:40
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------


Multi-person analysis selected.

--> A person was reconstructed if the lines from cameras to their keypoints intersected within 0.1 m and if the calculated affinity stayed above 0.2 after excluding points with likelihood below 0.3.
--> Beware that people were sorted across cameras, but not across frames. This will be done in the triangulation stage.

Tracked json files are stored in /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-associated.

Associating persons took 00h00m00s.



=====================================================================
Running triangulation...
=====================================================================

---------------------------------------------------------------------
Triangulation of 2D points for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:35:41
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------

Trimming Participant 0 around frames (34, 60), Participant 1 around frames (34, 60).



PARTICIPANT 1

Mean reprojection error for Hip is 12.4 px (~ 0.023 m), reached with 0.5 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 11.0 px (~ 0.02 m), reached with 1.23 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 11.0 px (~ 0.02 m), reached with 0.38 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 10.4 px (~ 0.019 m), reached with 0.65 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 8.7 px (~ 0.016 m), reached with 1.31 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.6 px (~ 0.019 m), reached with 1.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 8.1 px (~ 0.015 m), reached with 0.69 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 12.5 px (~ 0.023 m), reached with 0.31 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 9.7 px (~ 0.018 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 9.3 px (~ 0.017 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 10.8 px (~ 0.02 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 11.0 px (~ 0.02 m), reached with 0.46 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 9.6 px (~ 0.017 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 10.7 px (~ 0.019 m), reached with 0.73 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 11.2 px (~ 0.02 m), reached with 1.15 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 4.2 px (~ 0.008 m), reached with 1.92 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 9.2 px (~ 0.017 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 7.4 px (~ 0.013 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 10.2 px (~ 0.019 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 12.4 px (~ 0.023 m), reached with 0.35 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 10.4 px (~ 0.019 m), reached with 0.73 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 8.6 px (~ 0.016 m), reached with 0.69 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 34 to 60 is 10.0 px, which roughly corresponds to 18.2 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.61 cameras had to be excluded to reach these thresholds.
Camera cam02 was excluded 12% of the time, Camera cam03: 9%, Camera cam04: 2%, and Camera cam01: 2%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P1_34-59.trc.


PARTICIPANT 2

Mean reprojection error for Hip is 8.4 px (~ 0.015 m), reached with 1.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 7.5 px (~ 0.014 m), reached with 1.5 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 7.5 px (~ 0.014 m), reached with 1.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 7.5 px (~ 0.014 m), reached with 1.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 11.0 px (~ 0.02 m), reached with 0.77 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 11.0 px (~ 0.02 m), reached with 0.54 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 9.0 px (~ 0.016 m), reached with 1.08 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 8.9 px (~ 0.016 m), reached with 1.5 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 8.0 px (~ 0.015 m), reached with 1.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 6.7 px (~ 0.012 m), reached with 1.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 7.3 px (~ 0.013 m), reached with 1.12 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 7.3 px (~ 0.013 m), reached with 1.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 5.5 px (~ 0.01 m), reached with 1.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 7.0 px (~ 0.013 m), reached with 0.92 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 10.7 px (~ 0.019 m), reached with 0.46 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 8.4 px (~ 0.015 m), reached with 1.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 5.0 px (~ 0.009 m), reached with 1.42 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 6.1 px (~ 0.011 m), reached with 0.92 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 8.4 px (~ 0.015 m), reached with 0.73 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 7.4 px (~ 0.013 m), reached with 0.92 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 6.2 px (~ 0.011 m), reached with 1.35 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 8.5 px (~ 0.015 m), reached with 1.15 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 34 to 60 is 7.9 px, which roughly corresponds to 14.4 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 1.08 cameras had to be excluded to reach these thresholds.
Camera cam01 was excluded 21% of the time, Camera cam02: 14%, Camera cam03: 11%, and Camera cam04: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P2_34-59.trc.



Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m01s.



=====================================================================
Running filtering...
=====================================================================

---------------------------------------------------------------------
Filtering 3D coordinates for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:35:42
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Filtering 3D coordinates for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:36:53
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------

--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P1_34-59_filt_butterworth.trc.

--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P2_34-59_filt_butterworth.trc.




---------------------------------------------------------------------
Augmentation process for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:37:31
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------

Subject height automatically calculated for Demo_MultiPerson_P1_34-59_filt_butterworth.trc: 1.7 m

Subject height automatically calculated for Demo_MultiPerson_P2_34-59_filt_butterworth.trc: 1.37 m

Using Stanford LSTM v0.3 augmenter model. Feet are not vertically offset to be at floor level.

Augmented marker coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM.trc.
Augmented trc files have been converted to c3d.
Using Stanford LSTM v0.3 augmenter model. Feet are not vertically offset to be at floor level.

Augmented marker coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM.trc.
Augmented trc files have been converted to c3d.

Marker augmentation took 00h00m00s.


---------------------------------------------------------------------
OpenSim scaling and inverse kinematics for Demo_MultiPerson, for all frames.
On Tuesday 17. June 2025, 13:37:37
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson
---------------------------------------------------------------------

Subject height automatically calculated for Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM.trc: 1.7 m

Subject height automatically calculated for Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM.trc: 1.37 m

Processing TRC file: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM.trc

Scaling...
	Done. OpenSim logs saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/opensim_logs.txt.
	Scaled model saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM_scaled.osim

Inverse Kinematics...
	Done. OpenSim logs saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/opensim_logs.txt.
	Joint angle data saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P1_34-59_filt_butterworth_LSTM.mot

Processing TRC file: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/pose-3d/Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM.trc

Scaling...
	Done. OpenSim logs saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/opensim_logs.txt.
	Scaled model saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM_scaled.osim

Inverse Kinematics...
	Done. OpenSim logs saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/opensim_logs.txt.
	Joint angle data saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_MultiPerson/kinematics/Demo_MultiPerson_P2_34-59_filt_butterworth_LSTM.mot


OpenSim scaling and inverse kinematics took 00h00m46s.

