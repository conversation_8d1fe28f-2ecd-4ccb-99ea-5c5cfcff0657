[2025-06-16 16:38:01.907] [info] Updating Model file from 40000 to latest format...
[2025-06-16 16:38:01.944] [info] Loaded model Pose2Sim_WithContactsAndMuscles from file /home/<USER>/miniconda3/envs/Pose2Sim/lib/python3.10/site-packages/Pose2Sim/OpenSim_Setup/Model_Pose2Sim_contacts_muscles.osim
[2025-06-16 16:38:02.112] [info] Processing subject Pose2Sim_scaled...
[2025-06-16 16:38:02.112] [info] Step 1: Loading generic model
[2025-06-16 16:38:02.168] [info] Loaded model Pose2Sim_WithContactsAndMuscles from file /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/kinematics/Demo_SinglePerson_1-97_filt_butterworth_LSTM.osim
[2025-06-16 16:38:02.265] [info] Step 2: Scaling generic model
[2025-06-16 16:38:02.422] [info] Wrote model file '/home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/kinematics/Demo_SinglePerson_1-97_filt_butterworth_LSTM.osim' from model.
[2025-06-16 16:38:02.517] [info] Loaded model Pose2Sim_scaled from file /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/kinematics/Demo_SinglePerson_1-97_filt_butterworth_LSTM.osim
[2025-06-16 16:38:02.594] [cout] [info] 
               MODEL: Pose2Sim_scaled
         coordinates: 62
              forces: 330
           actuators: 318
             muscles: 318
            analyses: 0
              probes: 0
              bodies: 30
              joints: 30
         constraints: 20
             markers: 52
         controllers: 0
  contact geometries: 13
misc modelcomponents: 0
[2025-06-16 16:38:02.594] [info] Running tool .
[2025-06-16 16:38:03.005] [info] Frame 0 (t = 0.0166667):	 total squared error = 0.0227435, marker error: RMS = 0.0229982, max = 0.0549849 (L_shoulder_study)
[2025-06-16 16:38:03.132] [info] Frame 1 (t = 0.0333333):	 total squared error = 0.0196202, marker error: RMS = 0.0213608, max = 0.0507170 (L_shoulder_study)
[2025-06-16 16:38:03.229] [info] Frame 2 (t = 0.05):	 total squared error = 0.0192874, marker error: RMS = 0.0211788, max = 0.0452555 (L_shoulder_study)
[2025-06-16 16:38:03.445] [info] Frame 3 (t = 0.0666667):	 total squared error = 0.0194733, marker error: RMS = 0.0212807, max = 0.0404645 (L_shoulder_study)
[2025-06-16 16:38:03.546] [info] Frame 4 (t = 0.0833333):	 total squared error = 0.0212788, marker error: RMS = 0.0222453, max = 0.0401322 (L_shoulder_study)
[2025-06-16 16:38:03.630] [info] Frame 5 (t = 0.1):	 total squared error = 0.0219377, marker error: RMS = 0.0225871, max = 0.0383774 (L_shoulder_study)
[2025-06-16 16:38:03.780] [info] Frame 6 (t = 0.116667):	 total squared error = 0.0223552, marker error: RMS = 0.0228010, max = 0.0384335 (r_calc_study)
[2025-06-16 16:38:03.858] [info] Frame 7 (t = 0.133333):	 total squared error = 0.0227605, marker error: RMS = 0.0230068, max = 0.0383999 (r_calc_study)
[2025-06-16 16:38:03.936] [info] Frame 8 (t = 0.15):	 total squared error = 0.0233321, marker error: RMS = 0.0232939, max = 0.038197 (r_calc_study)
[2025-06-16 16:38:04.073] [info] Frame 9 (t = 0.166667):	 total squared error = 0.0244406, marker error: RMS = 0.0238408, max = 0.037827 (r_calc_study)
[2025-06-16 16:38:04.184] [info] Frame 10 (t = 0.183333):	 total squared error = 0.0259214, marker error: RMS = 0.0245524, max = 0.0374035 (r_calc_study)
[2025-06-16 16:38:04.279] [info] Frame 11 (t = 0.2):	 total squared error = 0.0275443, marker error: RMS = 0.0253094, max = 0.0400027 (r_shoulder_study)
[2025-06-16 16:38:04.459] [info] Frame 12 (t = 0.216667):	 total squared error = 0.0287167, marker error: RMS = 0.0258424, max = 0.0448206 (r_shoulder_study)
[2025-06-16 16:38:04.775] [info] Frame 13 (t = 0.233333):	 total squared error = 0.0290499, marker error: RMS = 0.0259919, max = 0.04761 (r_shoulder_study)
[2025-06-16 16:38:04.973] [info] Frame 14 (t = 0.25):	 total squared error = 0.0282107, marker error: RMS = 0.0256137, max = 0.0484324 (r_shoulder_study)
[2025-06-16 16:38:05.084] [info] Frame 15 (t = 0.266667):	 total squared error = 0.0263749, marker error: RMS = 0.0247663, max = 0.0475037 (r_shoulder_study)
[2025-06-16 16:38:05.188] [info] Frame 16 (t = 0.283333):	 total squared error = 0.0239417, marker error: RMS = 0.0235963, max = 0.045329 (r_shoulder_study)
[2025-06-16 16:38:05.288] [info] Frame 17 (t = 0.3):	 total squared error = 0.0216992, marker error: RMS = 0.022464, max = 0.0435662 (r_shoulder_study)
[2025-06-16 16:38:05.380] [info] Frame 18 (t = 0.316667):	 total squared error = 0.0198836, marker error: RMS = 0.0215037, max = 0.0429254 (r_shoulder_study)
[2025-06-16 16:38:05.472] [info] Frame 19 (t = 0.333333):	 total squared error = 0.0183783, marker error: RMS = 0.0206737, max = 0.0430741 (r_shoulder_study)
[2025-06-16 16:38:05.639] [info] Frame 20 (t = 0.35):	 total squared error = 0.0169776, marker error: RMS = 0.0198703, max = 0.0417538 (r_shoulder_study)
[2025-06-16 16:38:05.719] [info] Frame 21 (t = 0.366667):	 total squared error = 0.0159968, marker error: RMS = 0.0192878, max = 0.0435594 (r_shoulder_study)
[2025-06-16 16:38:05.873] [info] Frame 22 (t = 0.383333):	 total squared error = 0.0150154, marker error: RMS = 0.0186868, max = 0.0416494 (r_shoulder_study)
[2025-06-16 16:38:05.958] [info] Frame 23 (t = 0.4):	 total squared error = 0.0143762, marker error: RMS = 0.0182847, max = 0.0427301 (r_shoulder_study)
[2025-06-16 16:38:06.118] [info] Frame 24 (t = 0.416667):	 total squared error = 0.0138564, marker error: RMS = 0.0179511, max = 0.0428951 (r_shoulder_study)
[2025-06-16 16:38:06.228] [info] Frame 25 (t = 0.433333):	 total squared error = 0.0133239, marker error: RMS = 0.0176028, max = 0.0413538 (r_shoulder_study)
[2025-06-16 16:38:06.314] [info] Frame 26 (t = 0.45):	 total squared error = 0.0130380, marker error: RMS = 0.0174129, max = 0.0422968 (r_shoulder_study)
[2025-06-16 16:38:06.505] [info] Frame 27 (t = 0.466667):	 total squared error = 0.0126655, marker error: RMS = 0.0171624, max = 0.0409833 (r_shoulder_study)
[2025-06-16 16:38:06.641] [info] Frame 28 (t = 0.483333):	 total squared error = 0.0123744, marker error: RMS = 0.016964, max = 0.0403936 (r_shoulder_study)
[2025-06-16 16:38:06.745] [info] Frame 29 (t = 0.5):	 total squared error = 0.0122604, marker error: RMS = 0.0168856, max = 0.0410694 (r_shoulder_study)
[2025-06-16 16:38:06.845] [info] Frame 30 (t = 0.516667):	 total squared error = 0.0122579, marker error: RMS = 0.0168839, max = 0.0415942 (r_shoulder_study)
[2025-06-16 16:38:06.951] [info] Frame 31 (t = 0.533333):	 total squared error = 0.0122701, marker error: RMS = 0.0168924, max = 0.0412621 (r_shoulder_study)
[2025-06-16 16:38:07.105] [info] Frame 32 (t = 0.55):	 total squared error = 0.0123194, marker error: RMS = 0.0169262, max = 0.0408555 (r_shoulder_study)
[2025-06-16 16:38:07.204] [info] Frame 33 (t = 0.566667):	 total squared error = 0.0124525, marker error: RMS = 0.0170175, max = 0.0415371 (r_shoulder_study)
[2025-06-16 16:38:07.267] [info] Frame 34 (t = 0.583333):	 total squared error = 0.0125443, marker error: RMS = 0.0170800, max = 0.0422911 (r_shoulder_study)
[2025-06-16 16:38:07.357] [info] Frame 35 (t = 0.6):	 total squared error = 0.0125077, marker error: RMS = 0.0170551, max = 0.0425848 (r_shoulder_study)
[2025-06-16 16:38:07.420] [info] Frame 36 (t = 0.616667):	 total squared error = 0.0125256, marker error: RMS = 0.0170673, max = 0.0434059 (r_shoulder_study)
[2025-06-16 16:38:07.519] [info] Frame 37 (t = 0.633333):	 total squared error = 0.0124121, marker error: RMS = 0.0169898, max = 0.0432325 (r_shoulder_study)
[2025-06-16 16:38:07.619] [info] Frame 38 (t = 0.65):	 total squared error = 0.0124333, marker error: RMS = 0.0170043, max = 0.043657 (r_shoulder_study)
[2025-06-16 16:38:07.718] [info] Frame 39 (t = 0.666667):	 total squared error = 0.0125313, marker error: RMS = 0.0170712, max = 0.0440942 (r_shoulder_study)
[2025-06-16 16:38:07.822] [info] Frame 40 (t = 0.683333):	 total squared error = 0.0127468, marker error: RMS = 0.0172173, max = 0.0447014 (r_shoulder_study)
[2025-06-16 16:38:07.916] [info] Frame 41 (t = 0.7):	 total squared error = 0.0130858, marker error: RMS = 0.0174448, max = 0.0454394 (r_shoulder_study)
[2025-06-16 16:38:08.000] [info] Frame 42 (t = 0.716667):	 total squared error = 0.0135601, marker error: RMS = 0.0177581, max = 0.0461050 (r_shoulder_study)
[2025-06-16 16:38:08.061] [info] Frame 43 (t = 0.733333):	 total squared error = 0.0141168, marker error: RMS = 0.018119, max = 0.0467748 (r_shoulder_study)
[2025-06-16 16:38:08.154] [info] Frame 44 (t = 0.75):	 total squared error = 0.0146344, marker error: RMS = 0.0184482, max = 0.0466227 (r_shoulder_study)
[2025-06-16 16:38:08.240] [info] Frame 45 (t = 0.766667):	 total squared error = 0.0151664, marker error: RMS = 0.0187805, max = 0.0466704 (r_shoulder_study)
[2025-06-16 16:38:08.436] [info] Frame 46 (t = 0.783333):	 total squared error = 0.0157033, marker error: RMS = 0.01911, max = 0.0492484 (r_shoulder_study)
[2025-06-16 16:38:08.514] [info] Frame 47 (t = 0.8):	 total squared error = 0.0160765, marker error: RMS = 0.0193358, max = 0.0484227 (r_shoulder_study)
[2025-06-16 16:38:08.619] [info] Frame 48 (t = 0.816667):	 total squared error = 0.0163592, marker error: RMS = 0.0195050, max = 0.0481939 (r_shoulder_study)
[2025-06-16 16:38:08.757] [info] Frame 49 (t = 0.833333):	 total squared error = 0.016616, marker error: RMS = 0.0196576, max = 0.0488169 (r_shoulder_study)
[2025-06-16 16:38:08.847] [info] Frame 50 (t = 0.85):	 total squared error = 0.0166828, marker error: RMS = 0.019697, max = 0.0477439 (r_shoulder_study)
[2025-06-16 16:38:08.938] [info] Frame 51 (t = 0.866667):	 total squared error = 0.0166411, marker error: RMS = 0.0196724, max = 0.0472005 (r_shoulder_study)
[2025-06-16 16:38:09.109] [info] Frame 52 (t = 0.883333):	 total squared error = 0.0166351, marker error: RMS = 0.0196688, max = 0.0518694 (r_shoulder_study)
[2025-06-16 16:38:09.181] [info] Frame 53 (t = 0.9):	 total squared error = 0.0164021, marker error: RMS = 0.0195306, max = 0.0510069 (r_shoulder_study)
[2025-06-16 16:38:09.284] [info] Frame 54 (t = 0.916667):	 total squared error = 0.0160257, marker error: RMS = 0.0193052, max = 0.0508892 (r_shoulder_study)
[2025-06-16 16:38:09.363] [info] Frame 55 (t = 0.933333):	 total squared error = 0.0155359, marker error: RMS = 0.0190079, max = 0.0508391 (r_shoulder_study)
[2025-06-16 16:38:09.473] [info] Frame 56 (t = 0.95):	 total squared error = 0.0150191, marker error: RMS = 0.018689, max = 0.0510164 (r_shoulder_study)
[2025-06-16 16:38:09.655] [info] Frame 57 (t = 0.966667):	 total squared error = 0.0144379, marker error: RMS = 0.0183239, max = 0.0496511 (r_shoulder_study)
[2025-06-16 16:38:09.753] [info] Frame 58 (t = 0.983333):	 total squared error = 0.0141383, marker error: RMS = 0.0181328, max = 0.0506738 (r_shoulder_study)
[2025-06-16 16:38:09.862] [info] Frame 59 (t = 1.0):	 total squared error = 0.0139737, marker error: RMS = 0.0180269, max = 0.0514864 (r_shoulder_study)
[2025-06-16 16:38:09.978] [info] Frame 60 (t = 1.01667):	 total squared error = 0.0140153, marker error: RMS = 0.0180538, max = 0.0526615 (r_shoulder_study)
[2025-06-16 16:38:10.085] [info] Frame 61 (t = 1.03333):	 total squared error = 0.0142639, marker error: RMS = 0.0182131, max = 0.0538435 (r_shoulder_study)
[2025-06-16 16:38:10.209] [info] Frame 62 (t = 1.05):	 total squared error = 0.0147099, marker error: RMS = 0.0184957, max = 0.0548861 (r_shoulder_study)
[2025-06-16 16:38:10.324] [info] Frame 63 (t = 1.06667):	 total squared error = 0.0153211, marker error: RMS = 0.018876, max = 0.0559564 (r_shoulder_study)
[2025-06-16 16:38:10.408] [info] Frame 64 (t = 1.08333):	 total squared error = 0.0160933, marker error: RMS = 0.0193459, max = 0.0568084 (r_shoulder_study)
[2025-06-16 16:38:10.567] [info] Frame 65 (t = 1.1):	 total squared error = 0.0168327, marker error: RMS = 0.0197853, max = 0.0567445 (r_shoulder_study)
[2025-06-16 16:38:10.655] [info] Frame 66 (t = 1.11667):	 total squared error = 0.0176612, marker error: RMS = 0.0202664, max = 0.05731 (r_shoulder_study)
[2025-06-16 16:38:10.749] [info] Frame 67 (t = 1.13333):	 total squared error = 0.0183685, marker error: RMS = 0.0206682, max = 0.0573347 (r_shoulder_study)
[2025-06-16 16:38:10.852] [info] Frame 68 (t = 1.15):	 total squared error = 0.0188171, marker error: RMS = 0.0209190, max = 0.0566757 (r_shoulder_study)
[2025-06-16 16:38:10.945] [info] Frame 69 (t = 1.16667):	 total squared error = 0.0190307, marker error: RMS = 0.0210375, max = 0.0556286 (r_shoulder_study)
[2025-06-16 16:38:11.046] [info] Frame 70 (t = 1.18333):	 total squared error = 0.0191355, marker error: RMS = 0.0210953, max = 0.0543148 (r_shoulder_study)
[2025-06-16 16:38:11.157] [info] Frame 71 (t = 1.2):	 total squared error = 0.0193836, marker error: RMS = 0.0212316, max = 0.0530439 (r_shoulder_study)
[2025-06-16 16:38:11.260] [info] Frame 72 (t = 1.21667):	 total squared error = 0.0199048, marker error: RMS = 0.0215152, max = 0.0522205 (r_shoulder_study)
[2025-06-16 16:38:11.419] [info] Frame 73 (t = 1.23333):	 total squared error = 0.0206696, marker error: RMS = 0.0219246, max = 0.0522078 (r_shoulder_study)
[2025-06-16 16:38:11.546] [info] Frame 74 (t = 1.25):	 total squared error = 0.0216185, marker error: RMS = 0.0224222, max = 0.0514542 (r_shoulder_study)
[2025-06-16 16:38:11.671] [info] Frame 75 (t = 1.26667):	 total squared error = 0.0225316, marker error: RMS = 0.0228908, max = 0.0502659 (r_shoulder_study)
[2025-06-16 16:38:11.774] [info] Frame 76 (t = 1.28333):	 total squared error = 0.0231653, marker error: RMS = 0.0232105, max = 0.0482539 (r_shoulder_study)
[2025-06-16 16:38:11.975] [info] Frame 77 (t = 1.3):	 total squared error = 0.0234501, marker error: RMS = 0.0233528, max = 0.0453218 (r_shoulder_study)
[2025-06-16 16:38:12.082] [info] Frame 78 (t = 1.31667):	 total squared error = 0.0234273, marker error: RMS = 0.0233414, max = 0.0429463 (L_shoulder_study)
[2025-06-16 16:38:12.171] [info] Frame 79 (t = 1.33333):	 total squared error = 0.0231353, marker error: RMS = 0.0231955, max = 0.042246 (L_shoulder_study)
[2025-06-16 16:38:12.268] [info] Frame 80 (t = 1.35):	 total squared error = 0.0226228, marker error: RMS = 0.0229371, max = 0.0414864 (L_shoulder_study)
[2025-06-16 16:38:12.368] [info] Frame 81 (t = 1.36667):	 total squared error = 0.0219898, marker error: RMS = 0.0226139, max = 0.0409534 (L_shoulder_study)
[2025-06-16 16:38:12.462] [info] Frame 82 (t = 1.38333):	 total squared error = 0.0213702, marker error: RMS = 0.0222931, max = 0.0410627 (L_shoulder_study)
[2025-06-16 16:38:12.568] [info] Frame 83 (t = 1.4):	 total squared error = 0.0208936, marker error: RMS = 0.0220431, max = 0.0419888 (L_shoulder_study)
[2025-06-16 16:38:12.663] [info] Frame 84 (t = 1.41667):	 total squared error = 0.0208412, marker error: RMS = 0.0220155, max = 0.0438693 (L_shoulder_study)
[2025-06-16 16:38:12.781] [info] Frame 85 (t = 1.43333):	 total squared error = 0.021333, marker error: RMS = 0.0222737, max = 0.0470749 (L_lelbow_study)
[2025-06-16 16:38:12.877] [info] Frame 86 (t = 1.45):	 total squared error = 0.0224239, marker error: RMS = 0.0228361, max = 0.0530164 (L_lelbow_study)
[2025-06-16 16:38:12.988] [info] Frame 87 (t = 1.46667):	 total squared error = 0.0240111, marker error: RMS = 0.0236304, max = 0.0567176 (L_lelbow_study)
[2025-06-16 16:38:13.101] [info] Frame 88 (t = 1.48333):	 total squared error = 0.0257981, marker error: RMS = 0.024494, max = 0.0598655 (L_lelbow_study)
[2025-06-16 16:38:13.194] [info] Frame 89 (t = 1.5):	 total squared error = 0.0275049, marker error: RMS = 0.0252913, max = 0.0615643 (L_shoulder_study)
[2025-06-16 16:38:13.301] [info] Frame 90 (t = 1.51667):	 total squared error = 0.0287143, marker error: RMS = 0.0258414, max = 0.0635069 (L_shoulder_study)
[2025-06-16 16:38:13.437] [info] Frame 91 (t = 1.53333):	 total squared error = 0.0292144, marker error: RMS = 0.0260654, max = 0.0642858 (L_shoulder_study)
[2025-06-16 16:38:13.673] [info] Frame 92 (t = 1.55):	 total squared error = 0.0286658, marker error: RMS = 0.0258195, max = 0.0617078 (L_shoulder_study)
[2025-06-16 16:38:13.793] [info] Frame 93 (t = 1.56667):	 total squared error = 0.0277767, marker error: RMS = 0.0254159, max = 0.0589106 (L_shoulder_study)
[2025-06-16 16:38:13.900] [info] Frame 94 (t = 1.58333):	 total squared error = 0.0266975, marker error: RMS = 0.0249173, max = 0.0555396 (L_shoulder_study)
[2025-06-16 16:38:13.999] [info] Frame 95 (t = 1.6):	 total squared error = 0.0257028, marker error: RMS = 0.0244487, max = 0.0527973 (L_shoulder_study)
[2025-06-16 16:38:14.180] [info] Frame 96 (t = 1.61667):	 total squared error = 0.024454, marker error: RMS = 0.0238474, max = 0.0467957 (L_shoulder_study)
[2025-06-16 16:38:14.192] [info] InverseKinematicsTool completed 97 frames in 11 second(s).
